import os
import sys
import pandas as pd
import stripe
import logging
from datetime import datetime
from typing import Dict, <PERSON>, Tuple, NamedTuple, Optional
from dotenv import load_dotenv
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import random

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Set up Stripe API key
stripe.api_key = os.getenv('STRIPE_API_KEY')

# Configure Stripe max retries
stripe.max_network_retries = 2

class SalesMetrics(NamedTuple):
    total_volume: float
    new_customer_volume: float
    existing_customer_volume: float
    refund_volume: float
    net_volume: float

class StripeRateLimiter:
    def __init__(self, requests_per_second: int = 25):  # Default to test mode limit
        self.requests_per_second = requests_per_second
        self.last_request_time = 0
        self.tokens = requests_per_second
        self.last_token_refresh = time.time()
    
    def wait_for_token(self):
        current_time = time.time()
        time_passed = current_time - self.last_token_refresh
        self.tokens = min(
            self.requests_per_second,
            self.tokens + time_passed * self.requests_per_second
        )
        self.last_token_refresh = current_time
        
        if self.tokens < 1:
            sleep_time = (1 - self.tokens) / self.requests_per_second
            time.sleep(sleep_time)
            self.tokens = 0
        else:
            self.tokens -= 1

# Global rate limiter
rate_limiter = StripeRateLimiter(25 if stripe.api_key.startswith('sk_test_') else 100)

def safe_stripe_call(func, *args, **kwargs) -> Optional[any]:
    """Execute Stripe API call with rate limiting and error handling."""
    rate_limiter.wait_for_token()
    
    try:
        return func(*args, **kwargs)
    except stripe.error.RateLimitError as e:
        # Implement exponential backoff
        for i in range(3):  # Maximum 3 retries
            wait_time = (2 ** i) + random.random()
            logger.warning(f"Rate limit hit, waiting {wait_time:.2f} seconds...")
            time.sleep(wait_time)
            try:
                return func(*args, **kwargs)
            except stripe.error.RateLimitError:
                continue
        logger.error("Rate limit exceeded after retries")
        raise
    except stripe.error.StripeError as e:
        logger.error(f"Stripe API error: {str(e)}")
        raise

def get_customer_email(row: pd.Series) -> str:
    """Extract the first non-null email from the row."""
    email_columns = [
        'Person - Email - Work',
        'Person - Email - Home',
        'Person - Email - Other'
    ]
    
    for col in email_columns:
        if pd.notna(row[col]):
            return row[col]
    return None

def get_customer_by_email(email: str) -> Optional[stripe.Customer]:
    """Get customer by email with retries."""
    try:
        customers = safe_stripe_call(
            stripe.Customer.list,
            email=email,
            limit=1
        )
        return customers.data[0] if customers.data else None
    except stripe.error.StripeError as e:
        logger.error(f"Error getting customer for email {email}: {str(e)}")
        return None

def get_customer_metrics(customer: stripe.Customer, start_time: int, end_time: int) -> SalesMetrics:
    """Get all metrics for a customer in one API call to minimize requests."""
    try:
        # Get all charges with refunds expanded in one call
        charges = safe_stripe_call(
            stripe.Charge.list,
            customer=customer.id,
            created={'gte': start_time, 'lte': end_time},
            expand=['data.refunds'],
            limit=100
        )
        
        total_charges = 0
        total_refunds = 0
        first_charge_date = None
        
        for charge in charges.auto_paging_iter():
            if charge.status == 'succeeded':
                if first_charge_date is None or charge.created < first_charge_date:
                    first_charge_date = charge.created
                
                charge_amount = charge.amount
                refund_amount = charge.amount_refunded
                
                net_charge = charge_amount - refund_amount
                if net_charge > 0:
                    total_charges += net_charge
                
                if refund_amount > 0:
                    for refund in charge.refunds:
                        if start_time <= refund.created <= end_time:
                            total_refunds += refund.amount
        
        # Convert from cents to dollars
        total_charges = total_charges / 100.0
        total_refunds = total_refunds / 100.0
        
        is_new_customer = first_charge_date and first_charge_date >= start_time
        
        return SalesMetrics(
            total_volume=total_charges,
            new_customer_volume=total_charges if is_new_customer else 0.0,
            existing_customer_volume=total_charges if not is_new_customer else 0.0,
            refund_volume=total_refunds,
            net_volume=total_charges - total_refunds
        )
    
    except stripe.error.StripeError as e:
        logger.error(f"Error getting metrics for customer {customer.id}: {str(e)}")
        raise

def process_owner_deals(owner: str, deals_df: pd.DataFrame, start_time: int, end_time: int) -> SalesMetrics:
    """Process all deals for an owner with optimized API calls."""
    # Get unique emails
    emails = set(
        email for email in deals_df.apply(
            lambda row: row.get('Person - Email - Work') or 
                       row.get('Person - Email - Home') or 
                       row.get('Person - Email - Other'),
            axis=1
        ) if pd.notna(email)
    )
    
    if not emails:
        return SalesMetrics(0.0, 0.0, 0.0, 0.0, 0.0)
    
    logger.info(f"Processing {len(emails)} unique customers for {owner}")
    
    # Initialize metrics
    total_metrics = SalesMetrics(0.0, 0.0, 0.0, 0.0, 0.0)
    processed_count = 0
    error_count = 0
    
    # Process emails with progress bar
    with tqdm(total=len(emails), desc=f"Processing {owner}'s customers") as pbar:
        for email in emails:
            try:
                customer = get_customer_by_email(email)
                if customer:
                    metrics = get_customer_metrics(customer, start_time, end_time)
                    total_metrics = SalesMetrics(
                        total_volume=total_metrics.total_volume + metrics.total_volume,
                        new_customer_volume=total_metrics.new_customer_volume + metrics.new_customer_volume,
                        existing_customer_volume=total_metrics.existing_customer_volume + metrics.existing_customer_volume,
                        refund_volume=total_metrics.refund_volume + metrics.refund_volume,
                        net_volume=total_metrics.net_volume + metrics.net_volume
                    )
                    processed_count += 1
                else:
                    logger.debug(f"No Stripe customer found for email: {email}")
            except stripe.error.StripeError as e:
                error_count += 1
                logger.error(f"Error processing customer {email}: {str(e)}")
                if error_count > 10:  # Fail fast if too many errors
                    raise Exception(f"Too many errors ({error_count}) processing customers")
            finally:
                pbar.update(1)
    
    logger.info(f"Completed processing {owner}: {processed_count} customers processed, {error_count} errors")
    return total_metrics

def analyze_sales():
    logger.info("Starting sales analysis...")
    
    try:
        # Read the CSV file
        logger.info("Reading Pipedrive CSV file...")
        df = pd.read_csv('pipedrive.csv')
        logger.info(f"Found {len(df)} total deals in CSV")
        
        # Get the timestamp range for October 2024
        start_time = int(datetime(2024, 11, 1).timestamp())
        end_time = int(datetime(2024, 12, 1).timestamp())
        logger.info(f"Analyzing period: Nov 1, 2024 - Nov 30, 2024")
        
        # Process each owner
        sales_by_owner = {}
        owners = [owner for owner in df['Deal - Owner'].unique() if pd.notna(owner)]
        
        for owner in owners:
            try:
                owner_deals = df[df['Deal - Owner'] == owner]
                metrics = process_owner_deals(owner, owner_deals, start_time, end_time)
                sales_by_owner[owner] = metrics
                
                logger.info(f"\nCompleted {owner}:")
                logger.info(f"  Total Volume: ${metrics.total_volume:,.2f}")
                logger.info(f"  New Customer Volume: ${metrics.new_customer_volume:,.2f}")
                logger.info(f"  Existing Customer Volume: ${metrics.existing_customer_volume:,.2f}")
                logger.info(f"  Refund Volume: ${metrics.refund_volume:,.2f}")
                logger.info(f"  Net Volume: ${metrics.net_volume:,.2f}")
            
            except Exception as e:
                logger.error(f"Fatal error processing owner {owner}: {str(e)}")
                raise
        
        # Print final results
        logger.info("\nFinal Sales Results (October 2024):")
        logger.info("-" * 60)
        
        # Sort by net volume
        sorted_owners = sorted(
            sales_by_owner.items(),
            key=lambda x: x[1].net_volume,
            reverse=True
        )
        
        # Calculate totals
        total_metrics = SalesMetrics(
            sum(m.total_volume for m in sales_by_owner.values()),
            sum(m.new_customer_volume for m in sales_by_owner.values()),
            sum(m.existing_customer_volume for m in sales_by_owner.values()),
            sum(m.refund_volume for m in sales_by_owner.values()),
            sum(m.net_volume for m in sales_by_owner.values())
        )
        
        # Print individual results
        for owner, metrics in sorted_owners:
            logger.info(f"\n{owner}:")
            logger.info(f"  Total Volume: ${metrics.total_volume:,.2f}")
            logger.info(f"  New Customer Volume: ${metrics.new_customer_volume:,.2f}")
            logger.info(f"  Existing Customer Volume: ${metrics.existing_customer_volume:,.2f}")
            logger.info(f"  Refund Volume: ${metrics.refund_volume:,.2f}")
            logger.info(f"  Net Volume: ${metrics.net_volume:,.2f}")
        
        # Print grand totals
        logger.info("\nGRAND TOTALS:")
        logger.info("-" * 60)
        logger.info(f"Total Volume: ${total_metrics.total_volume:,.2f}")
        logger.info(f"New Customer Volume: ${total_metrics.new_customer_volume:,.2f}")
        logger.info(f"Existing Customer Volume: ${total_metrics.existing_customer_volume:,.2f}")
        logger.info(f"Refund Volume: ${total_metrics.refund_volume:,.2f}")
        logger.info(f"Net Volume: ${total_metrics.net_volume:,.2f}")
    
    except Exception as e:
        logger.error(f"Fatal error: {str(e)}")
        raise

if __name__ == "__main__":
    if not stripe.api_key:
        logger.error("Error: STRIPE_API_KEY environment variable not set")
        sys.exit(1)

    analyze_sales()
