import os
import pandas as pd
import stripe
import logging
from datetime import datetime
from typing import Dict, List, Tuple
from dotenv import load_dotenv
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import time
from ratelimit import limits, sleep_and_retry

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Set up Stripe API key
stripe.api_key = os.getenv('STRIPE_API_KEY')

# Configure rate limiting (100 requests per second as per Stripe's general limit)
CALLS_PER_SECOND = 100
PERIOD = 1  # 1 second

@sleep_and_retry
@limits(calls=CALLS_PER_SECOND, period=PERIOD)
def rate_limited_stripe_call(func, *args, **kwargs):
    return func(*args, **kwargs)

def get_customer_email(row: pd.Series) -> str:
    """Extract the first non-null email from the row."""
    email_columns = [
        'Person - Email - Work',
        'Person - Email - Home',
        'Person - Email - Other'
    ]
    
    for col in email_columns:
        if pd.notna(row[col]):
            return row[col]
    return None

def get_stripe_payments(email: str, start_time: int, end_time: int) -> Tuple[str, float]:
    """Get total payments for a customer in the specified time period."""
    try:
        # First find the customer by email
        customers = rate_limited_stripe_call(
            stripe.Customer.list,
            email=email,
            limit=1
        )
        
        if not customers.data:
            return email, 0.0
        
        customer = customers.data[0]
        
        # Get all payments for this customer in the time period
        payments = rate_limited_stripe_call(
            stripe.PaymentIntent.list,
            customer=customer.id,
            created={
                'gte': start_time,
                'lte': end_time
            }
        )
        
        # Sum up successful payments
        total = sum(
            payment.amount
            for payment in payments.auto_paging_iter()
            if payment.status == 'succeeded'
        )
        
        return email, total / 100.0  # Convert cents to dollars
    
    except Exception as e:
        logger.error(f"Error processing email {email}: {str(e)}")
        return email, 0.0

def process_owner_chunk(chunk_data: Tuple[str, pd.DataFrame, int, int]) -> Tuple[str, float]:
    """Process a chunk of deals for a single owner."""
    owner, deals_df, start_time, end_time = chunk_data
    total_sales = 0.0
    
    # Get all valid emails for this owner's deals
    emails = [
        email for email in deals_df.apply(get_customer_email, axis=1)
        if email is not None
    ]
    
    # Process emails in parallel
    with ThreadPoolExecutor(max_workers=10) as executor:
        future_to_email = {
            executor.submit(get_stripe_payments, email, start_time, end_time): email
            for email in emails
        }
        
        # Process completed futures with a progress bar
        with tqdm(total=len(emails), desc=f"Processing {owner}", leave=False) as pbar:
            for future in as_completed(future_to_email):
                email = future_to_email[future]
                try:
                    _, amount = future.result()
                    total_sales += amount
                except Exception as e:
                    logger.error(f"Error processing {email}: {str(e)}")
                pbar.update(1)
    
    return owner, total_sales

def analyze_sales():
    logger.info("Starting sales analysis...")
    
    # Read the CSV file
    logger.info("Reading Pipedrive CSV file...")
    df = pd.read_csv('pipedrive.csv')
    logger.info(f"Found {len(df)} total deals in CSV")
    
    # Get the timestamp range for October 2024
    start_time = int(datetime(2024, 10, 1).timestamp())
    end_time = int(datetime(2024, 11, 1).timestamp())
    logger.info(f"Analyzing period: Oct 1, 2024 - Oct 31, 2024")
    
    # Prepare data for parallel processing
    owner_chunks = []
    for owner in df['Deal - Owner'].unique():
        if pd.notna(owner):
            owner_deals = df[df['Deal - Owner'] == owner]
            owner_chunks.append((owner, owner_deals, start_time, end_time))
    
    logger.info(f"Processing {len(owner_chunks)} salespeople in parallel...")
    
    # Process owners in parallel
    sales_by_owner = {}
    with ThreadPoolExecutor(max_workers=5) as executor:
        future_to_owner = {
            executor.submit(process_owner_chunk, chunk): chunk[0]
            for chunk in owner_chunks
        }
        
        # Process completed futures with a progress bar
        with tqdm(total=len(owner_chunks), desc="Overall Progress") as pbar:
            for future in as_completed(future_to_owner):
                owner = future_to_owner[future]
                try:
                    _, total_sales = future.result()
                    sales_by_owner[owner] = total_sales
                    logger.info(f"Completed {owner}: ${total_sales:,.2f}")
                except Exception as e:
                    logger.error(f"Error processing owner {owner}: {str(e)}")
                pbar.update(1)
    
    # Print final results
    logger.info("\nFinal Sales Results (October 2024):")
    logger.info("-" * 40)
    for owner, total in sorted(sales_by_owner.items(), key=lambda x: x[1], reverse=True):
        logger.info(f"{owner}: ${total:,.2f}")

if __name__ == "__main__":
    if not stripe.api_key:
        logger.error("Error: STRIPE_API_KEY environment variable not set")
    else:
        analyze_sales()
