import requests

# Placeholder for the Discord webhook URL
DISCORD_WEBHOOK_URL = "https://discord.com/api/webhooks/1284339743018061876/epaufoZmlLQ6qNVRuqigl_XiD-081Gm2V-6hHrKgoHlK58p4tY64n4u1rJbC-SOBUsgS"

def send_to_discord(message: str):
    """
    Send a message to Discord using the webhook URL.
    
    Args:
    message (str): The message to send to Discord.
    
    Returns:
    dict: A dictionary containing the status and a message.
    """
    try:
        response = requests.post(DISCORD_WEBHOOK_URL, json={"content": message})
        if response.status_code == 204:
            return {"status": "success", "message": "Message sent to Discord"}
        else:
            return {"status": "error", "message": f"Failed to send message. Status code: {response.status_code}"}
    except requests.RequestException as e:
        return {"status": "error", "message": f"Request failed: {str(e)}"}

def main():
    # Test the function
    result = send_to_discord("Stripe Hawk is live.")
    print(result)

if __name__ == "__main__":
    main()